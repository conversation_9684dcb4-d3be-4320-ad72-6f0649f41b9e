// WebSocket集成模块
import type { Ref } from 'vue'
import { wsService, WebSocketState } from '../../services/websocketService'
import type { TextMessage, SystemMessage } from '../../services/websocketService'
import { useWebSocketStore } from '../websocket'

export class WebSocketIntegration {
  private error: Ref<string>
  private webSocketStore: ReturnType<typeof useWebSocketStore>

  constructor(error: Ref<string>) {
    this.error = error
    this.webSocketStore = useWebSocketStore()
  }

  // 初始化WebSocket连接
  async initWebSocket(
    token: string | undefined,
    initDatabase: () => Promise<void>,
    handleIncomingMessage: (message: TextMessage) => Promise<void>,
    handleIncomingSystemMessage: (message: SystemMessage) => void
  ) {
    try {
      // 先初始化数据库
      await initDatabase()

      // 设置WebSocket事件监听
      this.setupEventListeners(handleIncomingMessage, handleIncomingSystemMessage)
      // 连接WebSocket
      await wsService.connect(token)
      // 延迟强制同步状态，确保组件能获取到正确的状态
      setTimeout(() => {
        wsService.forceStateSync()
      }, 200)
    } catch (err) {
      console.error('WebSocket连接失败:', err)
      this.error.value = err instanceof Error ? err.message : '连接失败'
      throw err
    }
  }

  // 设置WebSocket事件监听器
  private setupEventListeners(
    handleIncomingMessage: (message: TextMessage) => Promise<void>,
    handleIncomingSystemMessage: (message: SystemMessage) => void
  ) {
    // 状态变化监听
    wsService.on('onStateChange', (state) => {
      // 状态变化时同步到全局 store
      this.webSocketStore.setState(state)
    })
    // 消息接收监听
    wsService.on('onMessage', (message) => {
      handleIncomingMessage(message).catch((error) => {
        console.error('处理接收消息失败:', error)
        this.error.value = '处理消息失败'
      })
    })

    // 系统消息监听
    wsService.on('onSystemMessage', (message) => {
      handleIncomingSystemMessage(message)
    })

    // 错误监听
    wsService.on('onError', (errorMsg) => {
      this.error.value = errorMsg.message
      console.error('WebSocketIntegration - WebSocket错误:', errorMsg)
    })

    // 心跳监听
    wsService.on('onHeartbeat', () => {
      console.log('WebSocketIntegration - 收到心跳响应')
    })

    console.log('WebSocketIntegration - WebSocket事件监听器已设置')
  }

  // 断开WebSocket连接
  disconnectWebSocket() {
    try {
      wsService.disconnect()
      this.webSocketStore.setState(WebSocketState.DISCONNECTED)
      console.log('WebSocketIntegration - WebSocket连接已断开')
    } catch (err) {
      console.error('断开WebSocket连接失败:', err)
      this.error.value = err instanceof Error ? err.message : '断开连接失败'
    }
  }

  // 手动重连WebSocket
  manualReconnectWebSocket() {
    try {
      wsService.manualReconnect()
      console.log('WebSocketIntegration - 开始手动重连WebSocket')
    } catch (err) {
      console.error('手动重连WebSocket失败:', err)
      this.error.value = err instanceof Error ? err.message : '重连失败'
    }
  }

  // 检查WebSocket连接状态
  isConnected(): boolean {
    return wsService.isConnected()
  }

  // 获取WebSocket状态
  getState(): WebSocketState {
    return this.webSocketStore.state.value
  }

  // 发送文本消息
  sendTextMessage(receiverId: string, content: string): boolean {
    try {
      if (!this.isConnected()) {
        this.error.value = 'WebSocket未连接'
        return false
      }

      const success = wsService.sendTextMessage(receiverId, content)
      if (!success) {
        this.error.value = '消息发送失败'
      }
      return success
    } catch (err) {
      console.error('发送文本消息失败:', err)
      this.error.value = err instanceof Error ? err.message : '发送失败'
      return false
    }
  }

  // 强制同步状态
  forceStateSync() {
    try {
      wsService.forceStateSync()
      console.log('WebSocketIntegration - 强制同步状态完成')
    } catch (err) {
      console.error('强制同步状态失败:', err)
    }
  }

  // 清理事件监听器
  cleanup() {
    try {
      // 移除所有事件监听器
      wsService.off('onStateChange')
      wsService.off('onMessage')
      wsService.off('onSystemMessage')
      wsService.off('onError')
      wsService.off('onHeartbeat')
      console.log('WebSocketIntegration - 事件监听器已清理')
    } catch (err) {
      console.error('清理事件监听器失败:', err)
    }
  }

  // 获取连接信息
  getConnectionInfo() {
    return {
      isConnected: this.isConnected(),
      state: this.getState(),
      hasError: !!this.error.value,
      errorMessage: this.error.value
    }
  }

  // 重置错误状态
  clearError() {
    this.error.value = ''
  }
}
